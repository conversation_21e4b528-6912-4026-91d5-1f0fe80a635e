import React, { useState } from 'react';
import { Screen } from '@/app-components/layout/screen';
import './animations.css';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  FileText, 
  User, 
  Users, 
  Building2, 
  Calendar, 
  Search, 
  Filter,
  Eye,
  CheckCircle,
  Clock,
  AlertCircle,
  Scale,
  RefreshCw,
  Download
} from 'lucide-react';
import { 
  useGetOfferLetterCategoriesQuery,
  OfferLetterCategory 
} from '@/pages/OfferLetter/api/offerLetterApi';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import OfferLetterDetailModal from './OfferLetterDetailModal';
import { exportOfferLettersToCSV } from './utils/exportUtils';

type CategoryFilter = 'ALL' | 'ACTIVE' | 'COMPLETE';

const OfferLettersDashboard: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<CategoryFilter>('ALL');
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [selectedOfferLetter, setSelectedOfferLetter] = useState<OfferLetterCategory | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // Debounce search term to avoid too many API calls
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page when searching
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const queryParams = {
    CATEGORY: selectedCategory,
    page: currentPage,
    page_size: 20,
    ...(debouncedSearchTerm && { search: debouncedSearchTerm }),
  };

  // Debug logging
  React.useEffect(() => {
    console.log('API Query Params:', queryParams);
  }, [selectedCategory, currentPage, debouncedSearchTerm]);

  const {
    data: offerLettersData,
    isLoading,
    error,
    refetch
  } = useGetOfferLetterCategoriesQuery(queryParams);

  const handleCategoryChange = (category: CategoryFilter) => {
    setSelectedCategory(category);
    setCurrentPage(1);
  };

  const handleCardClick = (offerLetter: OfferLetterCategory) => {
    setSelectedOfferLetter(offerLetter);
    setIsDetailModalOpen(true);
  };

  const handleExport = () => {
    if (filteredOfferLetters.length === 0) {
      alert('No data to export');
      return;
    }
    exportOfferLettersToCSV(filteredOfferLetters);
  };

  const getCustomerTypeIcon = (type: string) => {
    switch (type) {
      case 'individual':
        return <User className="w-4 h-4" />;
      case 'company':
        return <Building2 className="w-4 h-4" />;
      case 'partner':
        return <Users className="w-4 h-4" />;
      case 'group':
        return <Users className="w-4 h-4" />;
      default:
        return <User className="w-4 h-4" />;
    }
  };

  const getStatusBadge = (isCompleted: boolean) => {
    return isCompleted ? (
      <Badge variant="default" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Completed
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800">
        <Clock className="w-3 h-3 mr-1" />
        In Progress
      </Badge>
    );
  };

  // Use server-side filtered results, with client-side fallback if search param not supported
  const filteredOfferLetters = React.useMemo(() => {
    const results = offerLettersData?.results || [];

    // If we have a search term but the API didn't filter (same count as without search),
    // fall back to client-side filtering
    if (searchTerm && !debouncedSearchTerm) {
      return results.filter(letter => {
        const searchLower = searchTerm.toLowerCase();
        return (
          letter.plot_number.toLowerCase().includes(searchLower) ||
          letter.lead_file.toLowerCase().includes(searchLower) ||
          letter.booking_id.toLowerCase().includes(searchLower) ||
          letter.customer_type.toLowerCase().includes(searchLower)
        );
      });
    }

    return results;
  }, [offerLettersData?.results, searchTerm, debouncedSearchTerm]);

  if (isLoading) {
    return (
      <Screen>
        <div className="space-y-6">
          {/* Header Skeleton */}
          <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg skeleton"></div>
                <div className="space-y-2">
                  <div className="h-6 w-48 bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
                  <div className="h-4 w-64 bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
                </div>
              </div>
              <div className="flex space-x-3">
                <div className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
                <div className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
              </div>
            </div>
          </div>

          {/* Filters Skeleton */}
          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-9 w-24 bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
              ))}
            </div>
            <div className="h-9 w-80 bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
          </div>

          {/* Cards Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
              <div key={i} className="border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-lg p-6 space-y-4">
                <div className="flex justify-between items-start">
                  <div className="flex items-center space-x-2">
                    <div className="w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
                    <div className="h-5 w-20 bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
                  </div>
                  <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded-full skeleton"></div>
                </div>
                <div className="space-y-3">
                  {[1, 2, 3, 4].map((j) => (
                    <div key={j} className="flex justify-between">
                      <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
                      <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
                    </div>
                  ))}
                </div>
                <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                  <div className="h-8 w-full bg-gray-200 dark:bg-gray-700 rounded skeleton"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Screen>
    );
  }

  if (error) {
    return (
      <Screen>
        <div className="flex items-center justify-center py-16">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Error Loading Data
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Failed to load offer letters. Please try again.
            </p>
            <Button onClick={() => refetch()} variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-8">
        {/* Professional Header Section */}
        <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Scale className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Offer Letters Dashboard</h1>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Manage and track legal offer letters • {offerLettersData?.count || 0} total records
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                className="border-gray-300 dark:border-gray-600"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              {/* <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                className="border-gray-300 dark:border-gray-600"
              >
                <Download className="w-4 h-4 mr-2" />
                Export CSV
              </Button> */}
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
          <div className="flex flex-wrap gap-2">
            {(['ALL', 'ACTIVE', 'COMPLETE'] as CategoryFilter[]).map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => handleCategoryChange(category)}
                className={`transition-all duration-200 ${
                  selectedCategory === category
                    ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg'
                    : 'hover:bg-blue-50 dark:hover:bg-blue-900/20'
                }`}
              >
                <Filter className="w-4 h-4 mr-2" />
                {category === 'ALL' ? 'All Letters' : category === 'ACTIVE' ? 'Active' : 'Completed'}
              </Button>
            ))}
          </div>
          
          <div className="relative w-full lg:w-80">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search by plot number, lead file, booking ID, or customer type..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Offer Letters Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredOfferLetters.map((offerLetter, index) => (
            <Card
              key={`${offerLetter.booking_id}-${index}`}
              className="group hover:shadow-lg transition-all duration-200 cursor-pointer border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-blue-300 dark:hover:border-blue-600 animate-fade-in-up"
              style={{ animationDelay: `${index * 50}ms` }}
              onClick={() => handleCardClick(offerLetter)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getCustomerTypeIcon(offerLetter.customer_type)}
                    <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                      {offerLetter.plot_number}
                    </CardTitle>
                  </div>
                  {getStatusBadge(offerLetter.is_completed)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Customer Type:</span>
                    <Badge variant="outline" className="capitalize">
                      {offerLetter.customer_type}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Lead File:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {offerLetter.lead_file}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Booking ID:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {offerLetter.booking_id}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Date:</span>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3 text-gray-400" />
                      <span className="font-medium text-gray-900 dark:text-white">
                        {new Date(offerLetter.date).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20 transition-colors"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredOfferLetters.length === 0 && !isLoading && (
          <div className="text-center py-16">
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No Offer Letters Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {searchTerm
                ? `No offer letters match your search "${searchTerm}"`
                : selectedCategory === 'ACTIVE'
                  ? 'No active offer letters found. This might mean all offer letters are completed.'
                  : selectedCategory === 'COMPLETE'
                    ? 'No completed offer letters found.'
                    : 'No offer letters available'
              }
            </p>
            {searchTerm && (
              <Button variant="outline" onClick={() => setSearchTerm('')}>
                Clear Search
              </Button>
            )}
          </div>
        )}

        {/* Pagination */}
        {offerLettersData && offerLettersData.num_pages > 1 && (
          <div className="flex items-center justify-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Page {currentPage} of {offerLettersData.num_pages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(offerLettersData.num_pages, prev + 1))}
              disabled={currentPage === offerLettersData.num_pages}
            >
              Next
            </Button>
          </div>
        )}

        {/* Detail Modal */}
        {selectedOfferLetter && (
          <OfferLetterDetailModal
            isOpen={isDetailModalOpen}
            onClose={() => {
              setIsDetailModalOpen(false);
              setSelectedOfferLetter(null);
            }}
            offerLetter={selectedOfferLetter}
          />
        )}
      </div>
    </Screen>
  );
};

export default OfferLettersDashboard;
