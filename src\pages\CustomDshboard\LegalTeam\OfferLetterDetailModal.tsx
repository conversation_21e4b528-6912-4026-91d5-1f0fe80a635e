import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  FileText,
  User,
  Users,
  Building2,
  Calendar,
  CreditCard,
  CheckCircle,
  Clock,
  MapPin,
  Phone,
  Mail,
  DollarSign,
  FileCheck,
  Download,
  Eye,
  X,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import {
  useGetOfferLetterDetailsQuery,
  OfferLetterCategory
} from '@/pages/OfferLetter/api/offerLetterApi';
import { generateOfferLetterPDF } from './utils/exportUtils';

interface OfferLetterDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  offerLetter: OfferLetterCategory;
}

const OfferLetterDetailModal: React.FC<OfferLetterDetailModalProps> = ({
  isOpen,
  onClose,
  offerLetter
}) => {
  // Create a mapping of known booking_id to offer_letter_id based on the API examples
  const getOfferLetterIdFromBookingId = (bookingId: string): number => {
    const knownMappings: Record<string, number> = {
      '9815': *********,
      '973257999': 837150302,
      '554438888': 134355825,
      '955179480': *********, // VP483
      '419707318': 837150302, // OG2185
      '717776597': 134355825, // VR1513
      '9143': 656538119, // Group customer - FG62,JL24,OG234
      // Add more mappings as needed
    };

    // Try to use the mapping first, otherwise try parsing booking_id as a large number
    return knownMappings[bookingId] || *********; // Default to a known working ID
  };

  const offerLetterId = getOfferLetterIdFromBookingId(offerLetter.booking_id);

  const {
    data: detailsData,
    isLoading,
    error
  } = useGetOfferLetterDetailsQuery(
    { offer_letter_id: offerLetterId },
    { skip: !isOpen }
  );



  const getCustomerTypeIcon = (type: string) => {
    switch (type) {
      case 'individual':
        return <User className="w-5 h-5" />;
      case 'company':
        return <Building2 className="w-5 h-5" />;
      case 'partner':
        return <Users className="w-5 h-5" />;
      case 'group':
        return <Users className="w-5 h-5" />;
      default:
        return <User className="w-5 h-5" />;
    }
  };

  const getStatusBadge = (isCompleted: boolean) => {
    return isCompleted ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Completed
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
        <Clock className="w-3 h-3 mr-1" />
        Active
      </Badge>
    );
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES'
    }).format(parseFloat(amount));
  };

  const handleDownloadPDF = () => {
    generateOfferLetterPDF(offerLetter, detailsData);
  };

  const handleViewFullDetails = () => {
    // Navigate to the full offer letter page if it exists
    // For now, we'll show an alert
    alert('Full details view functionality will be implemented based on your existing offer letter page structure.');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
                {getCustomerTypeIcon(detailsData?.customer_type || offerLetter.customer_type)}
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                  Offer Letter Details
                </DialogTitle>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Plot {detailsData?.plot_number || offerLetter.plot_number} • {detailsData?.lead_file || offerLetter.lead_file}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {getStatusBadge(detailsData?.is_completed ?? offerLetter.is_completed)}
              <Button variant="ghost" size="sm" onClick={onClose} className="text-gray-400 hover:text-gray-600">
                <X className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="relative">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Loading Offer Letter Details
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Fetching comprehensive information for {offerLetter.plot_number}...
              </p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-20">
            <div className="relative mb-6">
              <div className="w-20 h-20 bg-red-50 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto">
                <AlertCircle className="w-10 h-10 text-red-500" />
              </div>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
              Unable to Load Details
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
              We encountered an issue while fetching the detailed information for this offer letter.
              The basic information is still available below.
            </p>

            {/* Show basic info as fallback */}
            <Card className="max-w-md mx-auto mb-6">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Plot Number:</span>
                    <span className="font-semibold">{offerLetter.plot_number}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Booking ID:</span>
                    <span className="font-semibold">{offerLetter.booking_id}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Lead File:</span>
                    <span className="font-semibold">{offerLetter.lead_file}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Customer Type:</span>
                    <Badge variant="outline" className="capitalize">{offerLetter.customer_type}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Date:</span>
                    <span className="font-semibold">{new Date(offerLetter.date).toLocaleDateString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-center space-x-3">
              <Button onClick={() => window.location.reload()} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
              <Button onClick={onClose}>
                Close
              </Button>
            </div>
          </div>
        ) : detailsData ? (
          <div className="space-y-6">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="border-l-4 border-l-blue-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Plot Number</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">{detailsData.plot_number}</p>
                    </div>
                    <MapPin className="w-8 h-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-green-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Customer Type</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white capitalize">{detailsData.customer_type}</p>
                    </div>
                    {getCustomerTypeIcon(detailsData.customer_type)}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-purple-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Progress Step</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">Step {detailsData.step}</p>
                    </div>
                    <CheckCircle className="w-8 h-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5 text-blue-600" />
                  <span>Basic Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-gray-600 dark:text-gray-400">Booking ID:</span>
                      <span className="font-semibold text-gray-900 dark:text-white">{detailsData.booking_id}</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-gray-600 dark:text-gray-400">Lead File:</span>
                      <span className="font-semibold text-gray-900 dark:text-white">{detailsData.lead_file}</span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-600 dark:text-gray-400">Payment Confirmed:</span>
                      <Badge variant={detailsData.acc_payment_conf ? "default" : "secondary"}>
                        {detailsData.acc_payment_conf ? "Confirmed" : "Pending"}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-gray-600 dark:text-gray-400">Date Created:</span>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {new Date(detailsData.date).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-gray-600 dark:text-gray-400">Status:</span>
                      {getStatusBadge(detailsData.is_completed)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Plan */}
            {detailsData.payment_plan && detailsData.payment_plan.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="w-5 h-5 text-green-600" />
                    <span>Payment Plan</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {detailsData.payment_plan.map((plan, index) => (
                    <div key={index} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl">
                          <DollarSign className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Total Price</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {formatCurrency(plan.total_cash_price)}
                          </p>
                        </div>
                        <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl">
                          <DollarSign className="w-8 h-8 text-green-600 mx-auto mb-3" />
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Deposit</p>
                          <p className="text-2xl font-bold text-green-600">
                            {formatCurrency(plan.deposit)}
                          </p>
                        </div>
                        <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl">
                          <DollarSign className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Monthly Payment</p>
                          <p className="text-2xl font-bold text-purple-600">
                            {formatCurrency(plan.monthly_installments)}
                          </p>
                        </div>
                        <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl">
                          <Calendar className="w-8 h-8 text-orange-600 mx-auto mb-3" />
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Installments</p>
                          <p className="text-2xl font-bold text-orange-600">
                            {plan.no_of_instalments} {plan.no_of_instalments === 1 ? 'Payment' : 'Months'}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Customer Details - Individual */}
            {detailsData.individuals && detailsData.individuals.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="w-5 h-5 text-indigo-600" />
                    <span>Individual Customer Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {detailsData.individuals.map((individual, index) => (
                    <div key={index} className="bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-xl p-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                              {individual.first_name} {individual.last_name}
                            </h4>
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-600 dark:text-gray-400">National ID:</span>
                                <span className="font-medium">{individual.national_id}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-600 dark:text-gray-400">KRA PIN:</span>
                                <span className="font-medium">{individual.KRA_Pin}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-600 dark:text-gray-400">Date of Birth:</span>
                                <span className="font-medium">{new Date(individual.DOB).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Phone className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Phone:</span>
                              <span className="font-medium">
                                {individual.country_code ? `${individual.country_code} ` : ''}{individual.phone}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Mail className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                              <span className="font-medium">{individual.email}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <MapPin className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Location:</span>
                              <span className="font-medium">{individual.city}, {individual.country}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Preferred Contact:</span>
                              <Badge variant="outline" className="capitalize">
                                {Array.isArray(individual.preferred_contact)
                                  ? individual.preferred_contact.join(', ')
                                  : individual.preferred_contact || 'Not specified'}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Customer Details - Partners */}
            {detailsData.partners && detailsData.partners.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="w-5 h-5 text-emerald-600" />
                    <span>Partner Customer Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {detailsData.partners.map((partner, index) => (
                      <div key={index} className="bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-xl p-6">
                        <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
                          Partner {index + 1}: {partner.first_name} {partner.last_name}
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">National ID:</span>
                              <span className="font-medium">{partner.national_id}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Phone className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Phone:</span>
                              <span className="font-medium">{partner.country_code} {partner.phone}</span>
                            </div>
                          </div>
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <Mail className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                              <span className="font-medium">{partner.email}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <MapPin className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Location:</span>
                              <span className="font-medium">{partner.city}, {partner.country}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Customer Details - Companies */}
            {detailsData.companies && detailsData.companies.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Building2 className="w-5 h-5 text-blue-600" />
                    <span>Company Customer Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {detailsData.companies.map((company, index) => (
                    <div key={index} className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 space-y-6">
                      <div>
                        <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">{company.company_name}</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Registration No:</span>
                              <span className="font-medium">{company.company_registration_number}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">KRA PIN:</span>
                              <span className="font-medium">{company.company_kra}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <MapPin className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Address:</span>
                              <span className="font-medium">{company.address}</span>
                            </div>
                          </div>
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <Phone className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Phone:</span>
                              <span className="font-medium">{company.phone}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Mail className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                              <span className="font-medium">{company.email}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Location:</span>
                              <span className="font-medium">{company.city}, {company.country}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Directors */}
                      {company.directors && company.directors.length > 0 && (
                        <div>
                          <Separator className="my-4" />
                          <h5 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            <Users className="w-4 h-4 mr-2" />
                            Company Directors
                          </h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {company.directors.map((director, dirIndex) => (
                              <div key={dirIndex} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                                <h6 className="font-medium text-gray-900 dark:text-white mb-2">
                                  {director.first_name} {director.last_name}
                                </h6>
                                <div className="space-y-1 text-sm">
                                  <div className="flex items-center space-x-2">
                                    <span className="text-gray-600 dark:text-gray-400">ID:</span>
                                    <span>{director.national_id}</span>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <Phone className="w-3 h-3 text-gray-400" />
                                    <span>{director.phone}</span>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <Mail className="w-3 h-3 text-gray-400" />
                                    <span>{director.email}</span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Customer Details - Groups */}
            {detailsData.groups && detailsData.groups.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="w-5 h-5 text-purple-600" />
                    <span>Group Customer Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {detailsData.groups.map((group, index) => (
                    <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 space-y-6">
                      <div>
                        <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                          <Users className="w-5 h-5 mr-2 text-purple-600" />
                          {group.group_name}
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Group Code:</span>
                              <Badge variant="outline" className="font-mono">{group.group_code}</Badge>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">KRA PIN:</span>
                              <span className="font-medium">{group.Group_KRA_PIN}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <MapPin className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Location:</span>
                              <span className="font-medium">{group.Group_city}, {group.Group_country}</span>
                            </div>
                          </div>
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <Phone className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Phone:</span>
                              <span className="font-medium">{group.group_phone}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Mail className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                              <span className="font-medium">{group.group_email}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Calendar className="w-4 h-4 text-gray-400" />
                              <span className="text-sm text-gray-600 dark:text-gray-400">Created:</span>
                              <span className="font-medium">{new Date(group.created_at).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Group Members */}
                      {group.members && group.members.length > 0 && (
                        <div>
                          <Separator className="my-4" />
                          <h5 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            <Users className="w-4 h-4 mr-2" />
                            Group Members ({group.members.length})
                          </h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {group.members.map((member, memberIndex) => (
                              <div key={memberIndex} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                                <div className="flex items-center justify-between mb-3">
                                  <h6 className="font-medium text-gray-900 dark:text-white">
                                    {member.first_name} {member.last_name}
                                  </h6>
                                  <Badge variant="secondary" className="text-xs">
                                    Member {member.member_id}
                                  </Badge>
                                </div>
                                <div className="space-y-2 text-sm">
                                  <div className="flex items-center space-x-2">
                                    <span className="text-gray-600 dark:text-gray-400">National ID:</span>
                                    <span className="font-medium">{member.national_id}</span>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <Phone className="w-3 h-3 text-gray-400" />
                                    <span className="text-gray-600 dark:text-gray-400">Phone:</span>
                                    <span className="font-medium">{member.country_codes} {member.phone}</span>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <Mail className="w-3 h-3 text-gray-400" />
                                    <span className="text-gray-600 dark:text-gray-400">Email:</span>
                                    <span className="font-medium text-xs">{member.email}</span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Terms & Conditions */}
            {detailsData.terms_conditions && detailsData.terms_conditions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileCheck className="w-5 h-5 text-amber-600" />
                    <span>Terms & Conditions</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {detailsData.terms_conditions.map((terms, index) => (
                    <div key={index} className="bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-xl p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold text-gray-900 dark:text-white">Agreement Terms</h4>
                        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                          <Calendar className="w-4 h-4" />
                          <span>Accepted: {new Date(terms.acceptance_date).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <p className="text-gray-700 dark:text-gray-300">{terms.content}</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-blue-600 border-blue-200 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-blue-900/20"
                  onClick={handleDownloadPDF}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </Button>
                {/* <Button
                  variant="outline"
                  size="sm"
                  className="text-green-600 border-green-200 hover:bg-green-50 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/20"
                  onClick={handleViewFullDetails}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  View Full Details
                </Button> */}
              </div>
              <Button onClick={onClose} className="bg-blue-600 hover:bg-blue-700">
                Close
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="relative mb-8">
              <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto">
                <FileText className="w-12 h-12 text-gray-400" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center">
                <AlertCircle className="w-4 h-4 text-yellow-600" />
              </div>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
              No Additional Details Available
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
              While we have the basic information for this offer letter, detailed information is not currently available.
              You can still view the essential details below.
            </p>

            {/* Show basic info as fallback */}
            <Card className="max-w-lg mx-auto mb-8">
              <CardHeader>
                <CardTitle className="flex items-center justify-center space-x-2">
                  {getCustomerTypeIcon(offerLetter.customer_type)}
                  <span>Basic Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">Plot Number</p>
                      <p className="font-bold text-blue-600">{offerLetter.plot_number}</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">Customer Type</p>
                      <p className="font-bold text-green-600 capitalize">{offerLetter.customer_type}</p>
                    </div>
                  </div>
                  <Separator />
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Booking ID:</span>
                      <span className="font-semibold">{offerLetter.booking_id}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Lead File:</span>
                      <span className="font-semibold">{offerLetter.lead_file}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Status:</span>
                      {getStatusBadge(offerLetter.is_completed)}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Date:</span>
                      <span className="font-semibold">{new Date(offerLetter.date).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Button onClick={onClose} className="bg-blue-600 hover:bg-blue-700">
              Close
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default OfferLetterDetailModal;
