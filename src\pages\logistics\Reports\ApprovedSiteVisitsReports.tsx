// src/pages/Reports/ApprovedSiteVisitsReport.tsx
import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, MapPin, User, Building2, CheckCircle2, Clock, XCircle, Users, Car, MessageSquare, Briefcase } from "lucide-react";
import { useGetSpecialBookingsQuery, useGetVehiclesQuery } from "@/redux/slices/logistics";

const ApprovedSiteVisitsReport: React.FC<{ data: any }> = ({ data }) => {
  const [combinedResults, setCombinedResults] = useState<any[]>([]);

  // Extract date range parameters from data.params if available
  const dateRangeParams = data?.params || {};
  const { pickup_date__gte, pickup_date__lte } = dateRangeParams;

  // Get special assignments with Approved status - apply same date range as main data
  const { data: specialAssignmentsData } = useGetSpecialBookingsQuery({
    status: 'Approved',
    page: 1,
    page_size: 1000,
    // Use reservation_date for special assignments filtering (not pickup_date)
    reservation_date__gte: pickup_date__gte, // Filter by reservation_date for special assignments
    reservation_date__lte: pickup_date__lte, // Filter by reservation_date for special assignments
  });

  // Get vehicles data for proper vehicle information display
  const { data: vehiclesData } = useGetVehiclesQuery({
    page: 1,
    page_size: 1000,
  });

  // Enhanced helper function to get vehicle information
  const getVehicleInfo = (visit: any) => {
    const vehicles = vehiclesData?.data?.results || [];

    // If visit has vehicle object, use it directly
    if (visit.vehicle && typeof visit.vehicle === 'object') {
      return {
        make: visit.vehicle.make,
        model: visit.vehicle.model,
        registration: visit.vehicle.vehicle_registration || visit.vehicle.registration,
        id: visit.vehicle.id
      };
    }

    // If visit has vehicle ID, find it in vehicles list
    if (visit.vehicle && (typeof visit.vehicle === 'number' || typeof visit.vehicle === 'string')) {
      const vehicleId = typeof visit.vehicle === 'string' ? parseInt(visit.vehicle, 10) : visit.vehicle;
      const vehicleData = vehicles.find((v: any) => v.id === vehicleId);
      if (vehicleData) {
        return {
          make: vehicleData.make,
          model: vehicleData.model,
          registration: vehicleData.vehicle_registration || vehicleData.registration,
          id: vehicleData.id
        };
      }
    }

    // Check for vehicle_id field
    if (visit.vehicle_id) {
      const vehicleId = typeof visit.vehicle_id === 'string' ? parseInt(visit.vehicle_id, 10) : visit.vehicle_id;
      const vehicleData = vehicles.find((v: any) => v.id === vehicleId);
      if (vehicleData) {
        return {
          make: vehicleData.make,
          model: vehicleData.model,
          registration: vehicleData.vehicle_registration || vehicleData.registration,
          id: vehicleData.id
        };
      }
    }

    // Fallback to individual fields
    return {
      make: visit.vehicle_make,
      model: visit.vehicle_model,
      registration: visit.vehicle_registration
    };
  };

  // Combine site visits and special assignments with date range filtering
  useEffect(() => {
    // Get site visits from data prop
    const siteVisits = data?.results || data?.data?.results || [];
    
    // Get special assignments from query
    const specialAssignments = specialAssignmentsData?.data?.results || [];

    // Enhanced logging for debugging date filtering issues
    console.group('🔍 ApprovedSiteVisitsReports - Data Processing');
    console.log('📅 Date range params:', dateRangeParams);
    console.log('🏠 Site visits count:', siteVisits.length);
    console.log('📋 Special assignments count:', specialAssignments.length);
    console.log('📋 Raw special assignments sample:', specialAssignments.slice(0, 3).map((assignment: any) => ({
      id: assignment.id,
      reservation_date: assignment.reservation_date,
      destination: assignment.destination,
      status: assignment.status
    })));
    console.groupEnd();
    
    // Transform special assignments to match site visit structure
    const transformedAssignments = specialAssignments.map((assignment: any) => ({
      ...assignment,
      pickup_date: assignment.reservation_date,
      pickup_time: assignment.reservation_time,
      pickup_location: assignment.pickup_location,
      project: assignment.destination,
      marketer: typeof assignment.assigned_to === 'object'
        ? (assignment.assigned_to?.dp_name || assignment.assigned_to?.name || 'Unknown Department')
        : assignment.assigned_to || 'Unknown',
      is_special_assignment: true,
    }));

    // Apply strict date range filtering to ensure adherence to selected dates
    const filterByDateRange = (item: any) => {
      if (!pickup_date__gte && !pickup_date__lte) return true;

      const itemDate = item.pickup_date || item.reservation_date;
      const itemType = item.is_special_assignment ? 'Special Assignment' : 'Site Visit';

      if (!itemDate) {
        console.log(`❌ No date field for ${itemType}:`, item.id);
        return false;
      }

      try {
        // Parse date and normalize to start of day
        let date = new Date(itemDate);
        if (typeof itemDate === 'string' && !itemDate.includes('T')) {
          date = new Date(itemDate + 'T00:00:00');
        }
        date.setHours(0, 0, 0, 0);

        let isWithinRange = true;

        if (pickup_date__gte) {
          const startDate = new Date(pickup_date__gte);
          startDate.setHours(0, 0, 0, 0);
          if (date < startDate) {
            isWithinRange = false;
          }
        }

        if (pickup_date__lte) {
          const endDate = new Date(pickup_date__lte);
          endDate.setHours(0, 0, 0, 0);
          if (date > endDate) {
            isWithinRange = false;
          }
        }

        console.log(`📅 ApprovedSiteVisits Filter - ${itemType}:`, {
          id: item.id,
          itemDate: itemDate,
          normalizedDate: date.toISOString().split('T')[0],
          startDate: pickup_date__gte,
          endDate: pickup_date__lte,
          isWithinRange: isWithinRange,
          action: isWithinRange ? '✅ INCLUDED' : '❌ FILTERED OUT'
        });

        return isWithinRange;
      } catch (error) {
        console.warn(`❌ Date parsing error for ${itemType}:`, item.id, itemDate, error);
        return false;
      }
    };
    
    // Filter both datasets by date range
    const filteredSiteVisits = siteVisits.filter(filterByDateRange);
    const filteredAssignments = transformedAssignments.filter(filterByDateRange);

    console.log('📊 ApprovedSiteVisits Final Results:', {
      originalSiteVisits: siteVisits.length,
      originalSpecialAssignments: transformedAssignments.length,
      filteredSiteVisits: filteredSiteVisits.length,
      filteredSpecialAssignments: filteredAssignments.length,
      dateRange: { from: pickup_date__gte, to: pickup_date__lte },
      finalDates: {
        siteVisitDates: [...new Set(filteredSiteVisits.map((item: any) =>
          (item.pickup_date || '').split('T')[0]
        ))].sort(),
        specialAssignmentDates: [...new Set(filteredAssignments.map((item: any) =>
          (item.pickup_date || item.reservation_date || '').split('T')[0]
        ))].sort()
      }
    });

    // Combine filtered results
    setCombinedResults([...filteredSiteVisits, ...filteredAssignments]);
  }, [data, specialAssignmentsData, vehiclesData, pickup_date__gte, pickup_date__lte]);

  if (!combinedResults?.length) {
    return (
      <Card className="w-full">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <CheckCircle2 className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-muted-foreground">No Approved Visits</h3>
          <p className="text-sm text-muted-foreground mt-2">
            No approved site visits or special assignments found for the selected date range.
          </p>
          {(pickup_date__gte || pickup_date__lte) && (
            <div className="mt-4 text-xs text-muted-foreground bg-muted/30 p-3 rounded-md">
              <p className="font-medium">Date Range Filter Applied:</p>
              <p>From: {pickup_date__gte || 'Not specified'}</p>
              <p>To: {pickup_date__lte || 'Not specified'}</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusLower = status?.toLowerCase();
    switch (statusLower) {
      case 'approved':
        return <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200"><CheckCircle2 className="w-3 h-3 mr-1" />{status}</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200"><Clock className="w-3 h-3 mr-1" />{status}</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />{status}</Badge>;
      default:
        return <Badge variant="outline">{status || 'Unknown'}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '-';
    try {
      const time = timeString.includes('T') ? new Date(timeString) : new Date(`2000-01-01T${timeString}`);
      return time.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch {
      return timeString;
    }
  };

  const getClientCount = (item: any) => {
    if (item.site_visit_client && Array.isArray(item.site_visit_client)) {
      return item.site_visit_client.length;
    }
    if (item.clients && Array.isArray(item.clients)) {
      return item.clients.length;
    }
    return item.clients || item.client_count || 0;
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <CheckCircle2 className="h-5 w-5 text-green-600" />
          Approved Visits Report
        </CardTitle>
        <CardDescription>
          Overview of all approved site visits and special assignments with detailed information
        </CardDescription>
        {(pickup_date__gte || pickup_date__lte) && (
          <div className="mt-2 text-xs text-muted-foreground bg-muted/30 p-2 rounded-md inline-flex items-center">
            <CalendarDays className="h-3 w-3 mr-1" />
            Filtered: {pickup_date__gte ? formatDate(pickup_date__gte) : 'Any'} to {pickup_date__lte ? formatDate(pickup_date__lte) : 'Any'}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <CalendarDays className="h-4 w-4" />
                    Date & Time
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Briefcase className="h-4 w-4" />
                    Type
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Marketer
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    No. of Clients
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Project/Destination
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Driver
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <Car className="h-4 w-4" />
                    Vehicle
                  </div>
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Pickup Location
                  </div>
                </TableHead>
                <TableHead className="font-semibold">Status</TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4" />
                    Remarks
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {combinedResults.map((visit: any, i: number) => (
                <TableRow key={visit.id || i} className="hover:bg-muted/30 transition-colors">
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <CalendarDays className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{formatDate(visit.pickup_date)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{formatTime(visit.pickup_time)}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {visit.is_special_assignment ? (
                        <Badge variant="secondary" className="bg-orange-100 text-orange-800 hover:bg-orange-200">
                          <Briefcase className="w-3 h-3 mr-1" />
                          Special Assignment
                        </Badge>
                      ) : (
                        <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200">
                          <Building2 className="w-3 h-3 mr-1" />
                          Site Visit
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <span className="font-medium">
                        {typeof visit.marketer === 'object'
                          ? (visit.marketer?.fullnames || visit.marketer?.name || visit.marketer?.dp_name || 'Unknown')
                          : (visit.marketer || visit.marketer_name || '-')
                        }
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{getClientCount(visit)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span>{visit.project || visit.project_name || visit.special_assignment_destination || '-'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <span className="font-medium">
                        {typeof visit.driver === 'object'
                          ? (visit.driver?.fullnames || visit.driver?.name || visit.driver?.first_name + ' ' + visit.driver?.last_name || 'Unknown Driver')
                          : (visit.driver || visit.driver_name || '-')
                        }
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4 text-muted-foreground" />
                      <div className="text-sm">
                        {(() => {
                          const vehicleInfo = getVehicleInfo(visit);
                          return (
                            <>
                              <div className="font-medium">
                                {[vehicleInfo.make, vehicleInfo.model]
                                  .filter(Boolean)
                                  .join(" ") || visit.vehicle || '-'}
                              </div>
                              {vehicleInfo.registration && (
                                <div className="text-xs font-semibold text-primary bg-primary/10 px-2 py-1 rounded mt-1">
                                  {vehicleInfo.registration}
                                </div>
                              )}
                            </>
                          );
                        })()}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{visit.pickup_location || visit.location || '-'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(visit.status)}
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs">
                      {visit.remarks ? (
                        <div className="flex items-start gap-2">
                          <MessageSquare className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-muted-foreground">{visit.remarks}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="mt-4 text-sm text-muted-foreground">
          Showing {combinedResults.length} approved visit{combinedResults.length !== 1 ? 's' : ''} (site visits & special assignments)
          {(pickup_date__gte || pickup_date__lte) && (
            <span> • Date filtered: {pickup_date__gte ? formatDate(pickup_date__gte) : 'Any'} to {pickup_date__lte ? formatDate(pickup_date__lte) : 'Any'}</span>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
export default ApprovedSiteVisitsReport;
